import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  ArrowLeft,
  Check,
  Download,
  Mail,
  Phone,
  Zap,
  Shield,
  Gauge,
  Settings,
  Monitor,
  Database,
  Wifi,
  Battery,
  Thermometer,
  ChevronDown
} from 'lucide-react';
import PageLayout from '@/components/layout/PageLayout';

const MicroOhmMeterProduct = () => {
  const { productId } = useParams();
  const navigate = useNavigate();
  const [activeSection, setActiveSection] = useState('overview');
  const [dropdownOpen, setDropdownOpen] = useState(false);

  // Complete product data
  const productData = {
    ca6240: {
      id: 'ca6240',
      model: 'CA 6240',
      subtitle: '10A Micro-Ohmmeter',
      image: 'https://images.pexels.com/photos/158826/structure-light-led-movement-158826.jpeg?auto=compress&cs=tinysrgb&w=800',
      current: '10A',
      resistanceRange: '5μΩ to 399.9Ω',
      accuracy: '±0.25% ± 2 counts',
      price: 'Contact for pricing',
      description: 'The CA 6240 is a precision micro-ohmmeter designed for accurate measurement of very low resistances. Perfect for testing circuit breakers, transformer windings, and motor connections with exceptional reliability.',
      keyFeatures: [
        'Large backlit LCD display for clear readings',
        'Auto measurement mode for ease of use',
        'Automatic recording mode with manual override',
        'Auto power-off function to preserve battery',
        'Memory storage for 100 measurements',
        'Optical/USB communication interface',
        'Complete PC interface software included',
        'Portable and rugged design for field use'
      ],
      technicalSpecs: {
        'Resistance Range': '5μΩ to 399.9Ω',
        'Accuracy': '±0.25% ± 2 counts',
        'Test Current': 'Up to 10A',
        'Display': 'Backlit LCD',
        'Memory': '100 measurements',
        'Communication': 'Optical/USB link',
        'Power Supply': 'Rechargeable battery',
        'Operating Temperature': '-10°C to +50°C',
        'Dimensions': '280 x 220 x 120 mm',
        'Weight': '2.5 kg'
      },
      applications: [
        'Circuit breaker contact resistance testing',
        'Transformer winding resistance measurement',
        'Motor and generator winding testing',
        'Cable and conductor resistance verification',
        'Welding quality control testing',
        'Electrical connection integrity checks'
      ],
      advantages: [
        'High accuracy and repeatability',
        'Fast measurement cycles',
        'User-friendly interface',
        'Robust construction for field use',
        'Comprehensive data logging',
        'PC connectivity for analysis'
      ]
    },
    ca6255: {
      id: 'ca6255',
      model: 'CA 6255',
      subtitle: '10A Advanced Micro-Ohmmeter',
      image: 'https://images.pexels.com/photos/257736/pexels-photo-257736.jpeg?auto=compress&cs=tinysrgb&w=800',
      current: '10A',
      resistanceRange: '5mΩ to 2,500Ω',
      accuracy: '±0.25% ± 2 counts',
      price: 'Contact for pricing',
      description: 'The CA 6255 represents the next generation of micro-ohmmeters with enhanced features including automatic discharge system and expanded memory capacity for professional testing applications.',
      keyFeatures: [
        'Large backlit LCD display with enhanced visibility',
        'Auto measurement mode with intelligent detection',
        'Automatic discharge system for safety',
        'Auto power-off with configurable timing',
        'Extended memory for 1,500 measurements',
        'RS 232 communication interface',
        'Advanced PC interface software',
        'Enhanced safety features and protection'
      ],
      technicalSpecs: {
        'Resistance Range': '5mΩ to 2,500Ω',
        'Accuracy': '±0.25% ± 2 counts',
        'Test Current': 'Up to 10A',
        'Display': 'Backlit LCD with enhanced contrast',
        'Memory': '1,500 measurements',
        'Communication': 'RS 232 link',
        'Power Supply': 'Rechargeable Li-ion battery',
        'Operating Temperature': '-10°C to +55°C',
        'Dimensions': '290 x 230 x 130 mm',
        'Weight': '2.8 kg'
      },
      applications: [
        'Power system maintenance and testing',
        'Industrial equipment quality control',
        'Electrical installation verification',
        'Preventive maintenance programs',
        'Research and development testing',
        'Educational and training applications'
      ],
      advantages: [
        'Extended measurement range',
        'Automatic discharge for safety',
        'Large memory capacity',
        'Enhanced communication options',
        'Improved user interface',
        'Advanced protection systems'
      ]
    },
    ca6292: {
      id: 'ca6292',
      model: 'CA 6292',
      subtitle: '200A High-Current Micro-Ohmmeter',
      image: 'https://images.pexels.com/photos/371049/pexels-photo-371049.jpeg?auto=compress&cs=tinysrgb&w=800',
      current: '200A',
      resistanceRange: '0.1μΩ to 1Ω',
      accuracy: '±1%',
      price: 'Contact for pricing',
      description: 'The CA 6292 is a high-current micro-ohmmeter designed for heavy-duty applications requiring up to 200A test current. Features advanced cooling system and comprehensive protection for industrial environments.',
      keyFeatures: [
        'Backlit LCD screen with 4 lines x 20 characters',
        'Internal cooling system for continuous operation',
        'Normal and BSG (Both Sides Grounded) test modes',
        'Advanced protection against surges and overheating',
        'Massive memory capacity for 8,000 measurements',
        'USB communication for fast data transfer',
        'Comprehensive PC interface software',
        'High-current capability up to 200A'
      ],
      technicalSpecs: {
        'Resistance Range': '0.1μΩ to 1Ω',
        'Accuracy': '±1%',
        'Test Current': 'Up to 200A',
        'BSG Mode Current': 'Up to 50A (with MR6292 clamp)',
        'Display': 'Backlit LCD (4 lines x 20 characters)',
        'Memory': '8,000 measurements',
        'Communication': 'USB interface',
        'Cooling': 'Internal cooling system',
        'Operating Temperature': '0°C to +40°C',
        'Dimensions': '350 x 280 x 180 mm',
        'Weight': '8.5 kg'
      },
      applications: [
        'Heavy industrial equipment testing',
        'Power plant maintenance operations',
        'High-current electrical system testing',
        'Large transformer testing',
        'Generator and motor testing',
        'Critical infrastructure maintenance'
      ],
      advantages: [
        'Ultra-high current capability',
        'Advanced cooling system',
        'Dual test modes (Normal/BSG)',
        'Massive data storage',
        'Comprehensive protection',
        'Professional-grade construction'
      ]
    }
  };

  const product = productData[productId];
  const productList = Object.values(productData);

  useEffect(() => {
    if (!product) {
      navigate('/measure/micro-ohmmeters');
    }
  }, [product, navigate]);

  if (!product) {
    return <div>Product not found</div>;
  }

  const sections = [
    { id: 'overview', label: 'Key Features', icon: Gauge },
    { id: 'specifications', label: 'Specifications', icon: Settings },
    { id: 'applications', label: 'Applications', icon: Zap }
  ];

  const FeatureIcon = ({ feature }) => {
    if (feature.toLowerCase().includes('display') || feature.toLowerCase().includes('lcd')) return <Monitor className="h-5 w-5" />;
    if (feature.toLowerCase().includes('memory') || feature.toLowerCase().includes('storage')) return <Database className="h-5 w-5" />;
    if (feature.toLowerCase().includes('communication') || feature.toLowerCase().includes('usb') || feature.toLowerCase().includes('rs 232')) return <Wifi className="h-5 w-5" />;
    if (feature.toLowerCase().includes('power') || feature.toLowerCase().includes('battery')) return <Battery className="h-5 w-5" />;
    if (feature.toLowerCase().includes('cooling') || feature.toLowerCase().includes('temperature')) return <Thermometer className="h-5 w-5" />;
    return <Check className="h-5 w-5" />;
  };

  return (
    <PageLayout hideHero={true}>
      {/* Main Title */}
      <div className="w-full py-6 text-center">
        <h1 className="text-4xl md:text-5xl font-extrabold text-black tracking-tight inline-block border-b-4 border-yellow-400 pb-2">
          Micro-Ohmmeters
        </h1>
      </div>
      {/* Back & Dropdown Bar */}
      <div className="w-full flex flex-col sm:flex-row items-center justify-between px-4 sm:px-8 py-4 gap-4">
        <button
          onClick={() => navigate('/measure/micro-ohmmeters')}
          className="flex items-center space-x-2 text-black font-semibold hover:text-yellow-700 transition-colors duration-200 text-lg"
        >
          <ArrowLeft className="h-5 w-5" />
          <span>Back to Products</span>
        </button>
        <div className="relative w-full sm:w-auto">
          <button
            onClick={() => setDropdownOpen(!dropdownOpen)}
            className="w-full sm:w-64 border border-yellow-500 text-black font-bold py-3 px-6 rounded-xl flex items-center justify-between shadow-md hover:bg-yellow-100 transition-all duration-200 bg-white"
          >
            <span>{product.model}</span>
            <ChevronDown className="h-5 w-5 ml-2" />
          </button>
          {dropdownOpen && (
            <div className="absolute z-50 mt-2 w-full sm:w-64 bg-white border border-yellow-400 rounded-xl shadow-lg max-h-60 overflow-y-auto">
              {productList.map((prod) => (
                <button
                  key={prod.id}
                  onClick={() => { setDropdownOpen(false); navigate(`/measure/micro-ohmmeters/product/${prod.id}`); }}
                  className={`w-full text-left px-6 py-3 text-black font-medium hover:bg-yellow-100 transition-colors duration-150 ${prod.id === product.id ? 'bg-yellow-200' : ''}`}
                >
                  {prod.model}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>
      <div className="min-h-screen bg-yellow-50" style={{ fontFamily: 'Open Sans, sans-serif' }}>
        {/* Header */}
        <div className="bg-white shadow-lg border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              {/* <div className="flex space-x-4">
                <button
                  className="min-w-[170px] px-4 py-2 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-semibold rounded-xl transition-colors duration-200 flex items-center justify-center space-x-2 shadow-sm text-base md:text-sm"
                  style={{ fontSize: '1rem', minHeight: '48px' }}
                >
                  <Download className="h-5 w-5" />
                  <span className="truncate">Download Brochure</span>
                </button>
                <button
                  className="min-w-[170px] px-4 py-2 bg-white border-2 border-yellow-400 text-gray-900 font-semibold rounded-xl hover:bg-yellow-50 transition-colors duration-200 flex items-center justify-center space-x-2 shadow-sm text-base md:text-sm"
                  style={{ fontSize: '1rem', minHeight: '48px' }}
                >
                  <Mail className="h-5 w-5" />
                  <span className="truncate">Request Quote</span>
                </button>
              </div> */}
            </div>
          </div>
        </div>

        {/* Product Hero Section */}
        <div className="bg-gradient-to-br from-yellow-100 to-yellow-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              {/* Specs Section */}
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                className="space-y-6 order-1 lg:order-1"
              >
                <div>
                  <div className="inline-block bg-yellow-400 px-4 py-2 rounded-full text-black font-bold text-sm mb-4">
                    {product.current} Test Current
                  </div>
                  <h1 className="text-4xl md:text-5xl font-bold text-black mb-4">
                    {product.model}
                  </h1>
                  <p className="text-xl text-yellow-700 font-semibold mb-6">
                    {product.subtitle}
                  </p>
                  <p className="text-lg text-black leading-relaxed mb-8">
                    {product.description}
                  </p>
                </div>

                {/* Quick Specs */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-white p-4 rounded-xl shadow-md">
                    <h4 className="font-semibold text-black mb-2">Resistance Range</h4>
                    <p className="text-yellow-700 font-bold">{product.resistanceRange}</p>
                  </div>
                  <div className="bg-white p-4 rounded-xl shadow-md">
                    <h4 className="font-semibold text-black mb-2">Accuracy</h4>
                    <p className="text-yellow-700 font-bold">{product.accuracy}</p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 pt-6">
                  <button onClick={() => navigate('/contact/sales')} className="flex-1 bg-yellow-400 hover:bg-yellow-500 text-black font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2">
                    <Phone className="h-5 w-5" />
                    <span>Request Demo</span>
                  </button>
                  <button onClick={() => window.open('/public/T&M April 2025.pdf', '_blank')} className="flex-1 bg-yellow-400 hover:bg-yellow-500 text-black font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2">
                    <Download className="h-5 w-5" />
                    <span>View Brochure</span>
                  </button>
                </div>
              </motion.div>
              {/* Product Image */}
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                className="relative order-2 lg:order-2"
              >
                <div className="relative h-64 bg-gradient-to-br from-yellow-200 to-yellow-50 rounded-3xl overflow-hidden p-4 flex items-center justify-center">
                  <div className="absolute inset-0 bg-gradient-to-br from-yellow-300 to-yellow-100 rounded-full opacity-20 blur-3xl transform scale-75"></div>
                  <motion.img
                    src={product.image}
                    alt={product.model}
                    className="max-h-full w-auto object-contain drop-shadow-2xl relative z-10"
                  />
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="shadow-lg sticky top-0 z-40">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-center">
            <div className="flex flex-row space-x-4 overflow-x-auto w-full justify-center">
              {sections.map(section => (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`py-4 px-2 border-b-2 font-bold text-base whitespace-nowrap flex items-center space-x-2 transition-colors duration-200 ${
                    activeSection === section.id
                      ? 'border-black text-black'
                      : 'border-transparent text-black hover:text-yellow-700 hover:border-yellow-500'
                  }`}
                >
                  <section.icon className="h-4 w-4" />
                  <span>{section.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Content Sections */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Overview Section */}
          {activeSection === 'overview' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="space-y-12"
            >
              <div className="bg-white rounded-2xl shadow-lg p-8">
                <h2 className="text-3xl font-bold text-gray-900 mb-8">Key Features</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {product.keyFeatures.map((feature, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: index * 0.1 }}
                      className="flex items-center space-x-4 p-4 rounded-xl hover:bg-yellow-50 transition-colors duration-200"
                    >
                      <div className="flex-shrink-0 w-10 h-10 min-w-[40px] bg-yellow-400 rounded-lg flex items-center justify-center">
                        <FeatureIcon feature={feature} />
                      </div>
                      <p className="flex-1 text-black font-medium leading-relaxed text-xl sm:text-xl lg:text-xl font-black mb-1 font-['Open_Sans']">{feature}</p>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          )}

          {/* Specifications Section */}
          {activeSection === 'specifications' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white rounded-2xl shadow-lg p-8"
            >
              <h2 className="text-3xl font-bold text-gray-900 mb-8">Technical Specifications</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {Object.entries(product.technicalSpecs).map(([key, value], index) => (
                  <motion.div
                    key={key}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.05 }}
                    className="bg-gray-50 rounded-xl p-6 hover:shadow-md transition-shadow duration-200"
                  >
                    <h4 className="font-semibold text-yellow-400 mb-2 border-b border-yellow-200 pb-2">{key}</h4>
                    <p className="text-black font-medium">{String(value)}</p>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {/* Applications Section */}
          {activeSection === 'applications' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white rounded-2xl shadow-lg p-8"
            >
              <h2 className="text-3xl font-bold text-gray-900 mb-8">Applications</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {product.applications.map((application, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                    className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl p-6 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
                  >
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="w-8 h-8 min-w-[32px] bg-yellow-400 rounded-lg flex items-center justify-center">
                        <Zap className="h-4 w-4 text-gray-900" />
                      </div>
                      <h4 className="flex-1 font-semibold text-gray-900 text-xl sm:text-xl lg:text-xl font-black mb-1 font-['Open_Sans']">{application}</h4>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </div>

        {/* Contact Section */}
        <div className="bg-gradient-to-br from-yellow-100 to-yellow-200 py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl md:text-4xl font-bold text-black mb-6">
                Ready to Experience Precision?
              </h2>
              <p className="text-xl text-black mb-8 font-medium">
                Contact our experts for personalized recommendations and demonstrations
              </p>
              <div className="flex justify-center">
                <button onClick={() => navigate('/contact/sales')} className="px-8 py-4 bg-yellow-400 hover:bg-yellow-500 text-black font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3">
                  <Mail className="h-5 w-5" />
                  <span>Contact Sales</span>
                </button>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default MicroOhmMeterProduct;